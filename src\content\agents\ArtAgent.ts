import { Vector3 } from '../../shared/types';
import { AssetProductionManager } from '../AssetProductionManager';
import { TextureOptimizer } from '../../rendering/TextureOptimizer';
import { GeometryOptimizer } from '../../rendering/GeometryOptimizer';
import { OverdrawOptimizer } from '../../rendering/OverdrawOptimizer';
import { PerformanceMetrics } from '../../rendering/PerformanceMetrics';

interface AssetSpec {
    id: string;
    type: 'building' | 'prop' | 'terrain' | 'vegetation';
    category: string;
    dimensions: { width: number; height: number; depth: number };
    polyCount: number;
    textureResolution: number;
    materialType: 'PBR' | 'simple';
    lodLevels: number;
}

interface VisualStyle {
    colorPalette: {
        primary: string;
        secondary: string;
        accent: string;
        neutral: string;
    };
    materialProperties: {
        roughness: number;
        metallic: number;
        emission: number;
    };
    weathering: number; // 0-1
    detailLevel: 'low' | 'medium' | 'high';
}

interface LightingSetup {
    ambientColor: string;
    ambientIntensity: number;
    sunColor: string;
    sunIntensity: number;
    sunAngle: { elevation: number; azimuth: number };
    shadows: {
        enabled: boolean;
        quality: 'low' | 'medium' | 'high';
        distance: number;
    };
    fog: {
        enabled: boolean;
        color: string;
        density: number;
        start: number;
        end: number;
    };
}

export class ArtAgent {
    private assetManager: AssetProductionManager;
    private textureOptimizer: TextureOptimizer;
    private geometryOptimizer: GeometryOptimizer;
    private overdrawOptimizer: OverdrawOptimizer;
    private metrics: PerformanceMetrics;
    
    private createdAssets: Map<string, AssetSpec> = new Map();
    private visualStyles: Map<string, VisualStyle> = new Map();
    private lightingSetups: Map<string, LightingSetup> = new Map();
    private assetLibrary: Map<string, any[]> = new Map();

    constructor() {
        this.metrics = PerformanceMetrics.getInstance();
        this.assetManager = new AssetProductionManager();
        this.textureOptimizer = new TextureOptimizer();
        this.geometryOptimizer = new GeometryOptimizer();
        this.overdrawOptimizer = new OverdrawOptimizer(this.metrics);
        
        this.initializeAssetLibrary();
        this.initializeVisualStyles();
    }

    async createPOIAssets(
        poiType: 'urban' | 'military' | 'industrial' | 'forest',
        poiId: string,
        layout: any
    ): Promise<{
        buildings: AssetSpec[];
        props: AssetSpec[];
        textures: any[];
        materials: any[];
    }> {
        console.log(`🎨 Art Agent: Criando assets para POI ${poiType} (${poiId})`);

        const visualStyle = this.getVisualStyleForPOI(poiType);
        
        // Cria edifícios principais
        const buildings = await this.createBuildings(poiType, layout.verticalElements, visualStyle);
        
        // Cria props e decorações
        const props = await this.createProps(poiType, layout.coverPoints, visualStyle);
        
        // Gera texturas otimizadas
        const textures = await this.generateOptimizedTextures(poiType, visualStyle);
        
        // Cria materiais PBR
        const materials = await this.createPBRMaterials(poiType, visualStyle);

        // Otimiza todos os assets
        await this.optimizeAssetCollection(buildings, props);

        console.log(`✅ Assets criados: ${buildings.length} edifícios, ${props.length} props`);

        return { buildings, props, textures, materials };
    }

    private async createBuildings(
        poiType: string,
        verticalElements: any[],
        style: VisualStyle
    ): Promise<AssetSpec[]> {
        const buildings: AssetSpec[] = [];

        for (const element of verticalElements) {
            if (element.type === 'building' || element.type === 'bunker' || 
                element.type === 'watchtower' || element.type === 'chimney') {
                
                const building = await this.generateBuildingAsset(
                    element.type,
                    element.height,
                    poiType,
                    style
                );
                
                buildings.push(building);
                this.createdAssets.set(building.id, building);
            }
        }

        return buildings;
    }

    private async generateBuildingAsset(
        buildingType: string,
        height: number,
        poiType: string,
        style: VisualStyle
    ): Promise<AssetSpec> {
        const baseWidth = this.getBuildingBaseSize(buildingType, poiType);
        
        const asset: AssetSpec = {
            id: `${buildingType}_${poiType}_${Date.now()}`,
            type: 'building',
            category: buildingType,
            dimensions: {
                width: baseWidth.width,
                height: height,
                depth: baseWidth.depth
            },
            polyCount: this.calculateOptimalPolyCount(buildingType, height),
            textureResolution: this.getOptimalTextureResolution(buildingType),
            materialType: 'PBR',
            lodLevels: this.calculateLODLevels(height)
        };

        // Otimiza geometria
        await this.optimizeAssetGeometry(asset);
        
        return asset;
    }

    private async createProps(
        poiType: string,
        coverPoints: Vector3[],
        style: VisualStyle
    ): Promise<AssetSpec[]> {
        const props: AssetSpec[] = [];
        const propTypes = this.getPropTypesForPOI(poiType);

        // Distribui props pelos pontos de cobertura
        for (let i = 0; i < coverPoints.length; i++) {
            if (Math.random() < 0.7) { // 70% chance de ter um prop
                const propType = propTypes[Math.floor(Math.random() * propTypes.length)];
                const prop = await this.generatePropAsset(propType, poiType, style);
                
                props.push(prop);
                this.createdAssets.set(prop.id, prop);
            }
        }

        // Adiciona props decorativos adicionais
        const decorativeProps = await this.generateDecorativeProps(poiType, style);
        props.push(...decorativeProps);

        return props;
    }

    private async generatePropAsset(
        propType: string,
        poiType: string,
        style: VisualStyle
    ): Promise<AssetSpec> {
        const dimensions = this.getPropDimensions(propType);
        
        const asset: AssetSpec = {
            id: `${propType}_${poiType}_${Date.now()}`,
            type: 'prop',
            category: propType,
            dimensions,
            polyCount: this.calculatePropPolyCount(propType),
            textureResolution: 512, // Props usam texturas menores
            materialType: 'PBR',
            lodLevels: 2 // Props têm menos LODs
        };

        await this.optimizeAssetGeometry(asset);
        
        return asset;
    }

    private async generateOptimizedTextures(
        poiType: string,
        style: VisualStyle
    ): Promise<any[]> {
        const textures = [];
        const textureTypes = this.getTextureTypesForPOI(poiType);

        for (const textureType of textureTypes) {
            const textureData = await this.generateTextureData(textureType, style);
            
            // Otimiza textura usando TextureOptimizer
            const optimizedTexture = await this.textureOptimizer.optimizeTexture(
                textureData.data,
                textureData.width,
                textureData.height,
                {
                    format: 'BC7',
                    quality: 128,
                    generateMipmaps: true,
                    platform: 'desktop'
                }
            );

            textures.push({
                id: `${textureType}_${poiType}`,
                type: textureType,
                data: optimizedTexture,
                style: style
            });
        }

        return textures;
    }

    private async createPBRMaterials(
        poiType: string,
        style: VisualStyle
    ): Promise<any[]> {
        const materials = [];
        const materialTypes = this.getMaterialTypesForPOI(poiType);

        for (const materialType of materialTypes) {
            const material = {
                id: `${materialType}_${poiType}`,
                type: materialType,
                albedo: style.colorPalette.primary,
                roughness: style.materialProperties.roughness,
                metallic: style.materialProperties.metallic,
                emission: style.materialProperties.emission,
                normal: `${materialType}_normal`,
                ao: `${materialType}_ao`,
                weathering: style.weathering
            };

            materials.push(material);
        }

        return materials;
    }

    async setupPOILighting(
        poiType: string,
        poiId: string,
        timeOfDay: 'dawn' | 'day' | 'dusk' | 'night' = 'day'
    ): Promise<LightingSetup> {
        console.log(`💡 Art Agent: Configurando iluminação para POI ${poiType} (${timeOfDay})`);

        const lightingSetup = this.createLightingSetup(poiType, timeOfDay);
        this.lightingSetups.set(poiId, lightingSetup);

        // Otimiza iluminação para performance
        await this.optimizeLighting(lightingSetup);

        return lightingSetup;
    }

    private createLightingSetup(poiType: string, timeOfDay: string): LightingSetup {
        const baseSetups = {
            dawn: {
                ambientColor: '#4A5568',
                ambientIntensity: 0.3,
                sunColor: '#FFA726',
                sunIntensity: 0.8,
                sunAngle: { elevation: 15, azimuth: 90 }
            },
            day: {
                ambientColor: '#87CEEB',
                ambientIntensity: 0.4,
                sunColor: '#FFFFFF',
                sunIntensity: 1.0,
                sunAngle: { elevation: 60, azimuth: 180 }
            },
            dusk: {
                ambientColor: '#2D3748',
                ambientIntensity: 0.2,
                sunColor: '#FF7043',
                sunIntensity: 0.6,
                sunAngle: { elevation: 10, azimuth: 270 }
            },
            night: {
                ambientColor: '#1A202C',
                ambientIntensity: 0.1,
                sunColor: '#4299E1',
                sunIntensity: 0.3,
                sunAngle: { elevation: -10, azimuth: 0 }
            }
        };

        const base = baseSetups[timeOfDay] || baseSetups.day;
        
        return {
            ...base,
            shadows: {
                enabled: true,
                quality: 'high',
                distance: 100
            },
            fog: this.getFogSettingsForPOI(poiType, timeOfDay)
        };
    }

    private getFogSettingsForPOI(poiType: string, timeOfDay: string) {
        const fogSettings = {
            urban: { enabled: true, color: '#B0BEC5', density: 0.01, start: 50, end: 200 },
            military: { enabled: false, color: '#90A4AE', density: 0.005, start: 100, end: 300 },
            industrial: { enabled: true, color: '#78909C', density: 0.02, start: 30, end: 150 },
            forest: { enabled: true, color: '#A5D6A7', density: 0.015, start: 40, end: 180 }
        };

        return fogSettings[poiType] || fogSettings.urban;
    }

    private initializeAssetLibrary() {
        // Inicializa biblioteca de assets base para cada tipo de POI
        this.assetLibrary.set('urban', [
            'apartment_building', 'office_tower', 'shop_front', 'parking_garage',
            'street_lamp', 'traffic_light', 'bench', 'trash_can', 'car', 'bus_stop'
        ]);
        
        this.assetLibrary.set('military', [
            'bunker', 'watchtower', 'barracks', 'hangar', 'radar_dish',
            'sandbags', 'concrete_barrier', 'military_vehicle', 'weapon_rack', 'fuel_tank'
        ]);
        
        this.assetLibrary.set('industrial', [
            'factory_building', 'warehouse', 'chimney', 'cooling_tower', 'crane',
            'conveyor_belt', 'machinery', 'pipes', 'storage_tank', 'forklift'
        ]);
        
        this.assetLibrary.set('forest', [
            'tree_oak', 'tree_pine', 'rock_boulder', 'fallen_log', 'cabin',
            'campfire', 'tent', 'wooden_crate', 'hunting_blind', 'stream'
        ]);
    }

    private initializeVisualStyles() {
        this.visualStyles.set('urban', {
            colorPalette: {
                primary: '#546E7A',
                secondary: '#90A4AE',
                accent: '#FF7043',
                neutral: '#ECEFF1'
            },
            materialProperties: {
                roughness: 0.7,
                metallic: 0.1,
                emission: 0.0
            },
            weathering: 0.3,
            detailLevel: 'high'
        });

        this.visualStyles.set('military', {
            colorPalette: {
                primary: '#4E342E',
                secondary: '#6D4C41',
                accent: '#8BC34A',
                neutral: '#D7CCC8'
            },
            materialProperties: {
                roughness: 0.8,
                metallic: 0.2,
                emission: 0.0
            },
            weathering: 0.5,
            detailLevel: 'high'
        });

        this.visualStyles.set('industrial', {
            colorPalette: {
                primary: '#37474F',
                secondary: '#607D8B',
                accent: '#FF9800',
                neutral: '#CFD8DC'
            },
            materialProperties: {
                roughness: 0.6,
                metallic: 0.4,
                emission: 0.1
            },
            weathering: 0.7,
            detailLevel: 'medium'
        });

        this.visualStyles.set('forest', {
            colorPalette: {
                primary: '#2E7D32',
                secondary: '#4CAF50',
                accent: '#8BC34A',
                neutral: '#E8F5E8'
            },
            materialProperties: {
                roughness: 0.9,
                metallic: 0.0,
                emission: 0.0
            },
            weathering: 0.4,
            detailLevel: 'medium'
        });
    }

    private getVisualStyleForPOI(poiType: string): VisualStyle {
        return this.visualStyles.get(poiType) || this.visualStyles.get('urban')!;
    }

    private getBuildingBaseSize(buildingType: string, poiType: string) {
        const sizes = {
            building: { width: 15, depth: 15 },
            bunker: { width: 20, depth: 12 },
            watchtower: { width: 6, depth: 6 },
            chimney: { width: 4, depth: 4 },
            warehouse: { width: 25, depth: 40 },
            hangar: { width: 30, depth: 50 }
        };
        return sizes[buildingType] || { width: 10, depth: 10 };
    }

    private calculateOptimalPolyCount(buildingType: string, height: number): number {
        const baseCounts = {
            building: 800,
            bunker: 600,
            watchtower: 400,
            chimney: 200,
            warehouse: 1200,
            hangar: 1500
        };

        const baseCount = baseCounts[buildingType] || 500;
        const heightMultiplier = Math.max(1, height / 10);

        return Math.floor(baseCount * heightMultiplier);
    }

    private getOptimalTextureResolution(buildingType: string): number {
        const resolutions = {
            building: 2048,
            bunker: 1024,
            watchtower: 1024,
            chimney: 512,
            warehouse: 2048,
            hangar: 2048
        };
        return resolutions[buildingType] || 1024;
    }

    private calculateLODLevels(height: number): number {
        if (height > 30) return 4;
        if (height > 15) return 3;
        if (height > 5) return 2;
        return 1;
    }

    private getPropTypesForPOI(poiType: string): string[] {
        const propTypes = {
            urban: ['car', 'bench', 'trash_can', 'street_lamp', 'mailbox', 'fire_hydrant'],
            military: ['sandbags', 'concrete_barrier', 'weapon_rack', 'ammo_crate', 'fuel_barrel'],
            industrial: ['machinery', 'pipes', 'storage_tank', 'conveyor_belt', 'forklift'],
            forest: ['rock_boulder', 'fallen_log', 'campfire', 'wooden_crate', 'tent']
        };
        return propTypes[poiType] || propTypes.urban;
    }

    private getPropDimensions(propType: string) {
        const dimensions = {
            car: { width: 4.5, height: 1.5, depth: 2 },
            bench: { width: 2, height: 0.8, depth: 0.6 },
            trash_can: { width: 0.6, height: 1, depth: 0.6 },
            sandbags: { width: 2, height: 0.8, depth: 0.5 },
            machinery: { width: 3, height: 2.5, depth: 2 },
            rock_boulder: { width: 2, height: 1.5, depth: 1.8 }
        };
        return dimensions[propType] || { width: 1, height: 1, depth: 1 };
    }

    private calculatePropPolyCount(propType: string): number {
        const polyCounts = {
            car: 1500,
            bench: 200,
            trash_can: 150,
            sandbags: 300,
            machinery: 800,
            rock_boulder: 400
        };
        return polyCounts[propType] || 300;
    }

    private async generateDecorativeProps(poiType: string, style: VisualStyle): Promise<AssetSpec[]> {
        const decorativeProps: AssetSpec[] = [];
        const decorativeTypes = this.getDecorativeTypesForPOI(poiType);

        // Gera 5-10 props decorativos
        const count = 5 + Math.floor(Math.random() * 6);

        for (let i = 0; i < count; i++) {
            const propType = decorativeTypes[Math.floor(Math.random() * decorativeTypes.length)];
            const prop = await this.generatePropAsset(`decorative_${propType}`, poiType, style);
            decorativeProps.push(prop);
        }

        return decorativeProps;
    }

    private getDecorativeTypesForPOI(poiType: string): string[] {
        const decorativeTypes = {
            urban: ['flower_pot', 'newspaper_stand', 'bicycle', 'street_sign'],
            military: ['flag_pole', 'communication_antenna', 'guard_post', 'checkpoint'],
            industrial: ['warning_sign', 'safety_equipment', 'tool_rack', 'control_panel'],
            forest: ['bird_nest', 'mushroom_cluster', 'berry_bush', 'animal_track']
        };
        return decorativeTypes[poiType] || decorativeTypes.urban;
    }

    private getTextureTypesForPOI(poiType: string): string[] {
        const textureTypes = {
            urban: ['concrete', 'brick', 'asphalt', 'glass', 'metal'],
            military: ['camouflage', 'steel', 'concrete_bunker', 'canvas', 'rust'],
            industrial: ['corrugated_metal', 'painted_steel', 'concrete_industrial', 'rubber', 'plastic'],
            forest: ['bark', 'moss', 'stone', 'dirt', 'leaves']
        };
        return textureTypes[poiType] || textureTypes.urban;
    }

    private getMaterialTypesForPOI(poiType: string): string[] {
        const materialTypes = {
            urban: ['concrete_clean', 'brick_weathered', 'metal_painted', 'glass_clear'],
            military: ['steel_military', 'concrete_fortified', 'canvas_heavy', 'metal_rusted'],
            industrial: ['steel_industrial', 'concrete_stained', 'rubber_industrial', 'plastic_worn'],
            forest: ['wood_natural', 'stone_moss', 'dirt_organic', 'vegetation']
        };
        return materialTypes[poiType] || materialTypes.urban;
    }

    private async generateTextureData(textureType: string, style: VisualStyle): Promise<{
        data: Uint8Array;
        width: number;
        height: number;
    }> {
        // Simula geração de dados de textura
        const width = 1024;
        const height = 1024;
        const data = new Uint8Array(width * height * 4);

        // Gera padrão baseado no tipo de textura e estilo
        for (let i = 0; i < data.length; i += 4) {
            const color = this.generatePixelColor(textureType, style, i / 4);
            data[i] = color.r;
            data[i + 1] = color.g;
            data[i + 2] = color.b;
            data[i + 3] = 255;
        }

        return { data, width, height };
    }

    private generatePixelColor(textureType: string, style: VisualStyle, pixelIndex: number) {
        // Converte cor hex para RGB
        const hexToRgb = (hex: string) => {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : { r: 128, g: 128, b: 128 };
        };

        const baseColor = hexToRgb(style.colorPalette.primary);

        // Adiciona variação baseada no tipo de textura
        const variation = Math.random() * 0.2 - 0.1; // ±10% de variação

        return {
            r: Math.max(0, Math.min(255, baseColor.r + variation * 255)),
            g: Math.max(0, Math.min(255, baseColor.g + variation * 255)),
            b: Math.max(0, Math.min(255, baseColor.b + variation * 255))
        };
    }

    private async optimizeAssetGeometry(asset: AssetSpec): Promise<void> {
        // Simula otimização de geometria usando GeometryOptimizer
        const optimizationResult = await this.geometryOptimizer.optimizeGeometry(
            new Uint8Array(asset.polyCount * 12), // Simula dados de geometria
            {
                targetPolyCount: Math.floor(asset.polyCount * 0.8),
                preserveUVs: true,
                preserveNormals: true,
                generateLODs: true,
                lodCount: asset.lodLevels
            }
        );

        // Atualiza contagem de polígonos otimizada
        asset.polyCount = Math.floor(asset.polyCount * 0.8);
    }

    private async optimizeAssetCollection(buildings: AssetSpec[], props: AssetSpec[]): Promise<void> {
        console.log('🔧 Otimizando coleção de assets...');

        const allAssets = [...buildings, ...props];

        // Otimiza overdraw considerando todos os assets
        for (const asset of allAssets) {
            this.overdrawOptimizer.addRenderableObject({
                position: { x: 0, y: 0, z: 0 }, // Posição será definida na colocação
                isTransparent: false,
                boundingBox: {
                    min: { x: -asset.dimensions.width/2, y: 0, z: -asset.dimensions.depth/2 },
                    max: { x: asset.dimensions.width/2, y: asset.dimensions.height, z: asset.dimensions.depth/2 }
                }
            });
        }

        // Otimiza ordem de renderização
        const optimizedOrder = this.overdrawOptimizer.optimizeRenderOrder();

        console.log(`✅ ${allAssets.length} assets otimizados para renderização`);
    }

    private async optimizeLighting(lightingSetup: LightingSetup): Promise<void> {
        // Otimiza configurações de iluminação para performance
        if (lightingSetup.shadows.enabled) {
            // Ajusta qualidade de sombras baseado na performance
            const currentFPS = this.metrics.measureFPS();
            if (currentFPS < 500) {
                lightingSetup.shadows.quality = 'medium';
                lightingSetup.shadows.distance = 75;
            }
            if (currentFPS < 300) {
                lightingSetup.shadows.quality = 'low';
                lightingSetup.shadows.distance = 50;
            }
        }

        // Otimiza fog baseado na performance
        if (lightingSetup.fog.enabled) {
            const vramUsage = this.metrics.getVRAMUsage();
            if (vramUsage > 6000) { // > 6GB
                lightingSetup.fog.density *= 0.8;
                lightingSetup.fog.end *= 0.9;
            }
        }
    }

    async getCreatedAssets(): Promise<AssetSpec[]> {
        return Array.from(this.createdAssets.values());
    }

    async getVisualStyles(): Promise<Map<string, VisualStyle>> {
        return this.visualStyles;
    }

    async getLightingSetups(): Promise<Map<string, LightingSetup>> {
        return this.lightingSetups;
    }

    async getAssetLibrary(): Promise<Map<string, any[]>> {
        return this.assetLibrary;
    }
}
