{"name": "tactical-nexus", "version": "1.0.0", "description": "Project: Tactical Nexus - Battle Royale Game", "main": "src/server/index.ts", "scripts": {"start": "ts-node src/server/index.ts", "test": "jest", "build": "tsc", "dev": "nodemon", "generate-assets": "ts-node src/tests/generateDummyAssets.ts", "test:map": "ts-node src/tests/runMapTest.ts", "test:map-full": "npm run generate-assets && npm run test:map", "test:mass-production": "ts-node src/tests/runMassProduction.ts", "test:mass-production-quick": "ts-node src/tests/runMassProduction.ts --quick", "test:mass-production-no-log": "ts-node src/tests/runMassProduction.ts --no-logging", "production:start": "npm run test:mass-production", "production:validate": "npm run test:mass-production-quick"}, "keywords": ["fps", "battle-royale", "tactical", "game"], "author": "", "license": "MIT", "dependencies": {"meshoptimizer": "^0.23.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "jest": "^29.5.0", "nodemon": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.8.3"}}