import { Vector3 } from '../shared/types';
import { OptimizationMetrics } from './OptimizationMetrics';
import { ContentProductionSystem } from '../content/ContentProductionSystem';
import { OverdrawOptimizer } from './OverdrawOptimizer';
import { TextureOptimizer } from './TextureOptimizer';
import { PerformanceReport, AreaPerformanceMetrics } from '../shared/types';

interface PerformanceThresholds {
    targetFPS: number;
    maxOverdraw: number;
    maxVRAM: number;
    maxDrawCalls: number;
    maxTextureMemory: number;
    maxGeometryMemory: number;
}

export class PerformanceMetrics {
    private static instance: PerformanceMetrics;
    private contentSystem: ContentProductionSystem | null = null;
    private overdrawOptimizer: OverdrawOptimizer | null = null;
    private textureOptimizer: TextureOptimizer | null = null;
    
    private thresholds: PerformanceThresholds = {
        targetFPS: 999,
        maxOverdraw: 2.0,
        maxVRAM: 8192,
        maxDrawCalls: 2000,
        maxTextureMemory: 4096,
        maxGeometryMemory: 2048
    };

    private areaMetrics: Map<string, AreaPerformanceMetrics> = new Map();
    private warningCallbacks: ((warning: string) => void)[] = [];

    private constructor() {
        this.initializeMonitoring();
    }

    static getInstance(): PerformanceMetrics {
        if (!PerformanceMetrics.instance) {
            PerformanceMetrics.instance = new PerformanceMetrics();
        }
        return PerformanceMetrics.instance;
    }

    setContentSystem(system: ContentProductionSystem) {
        this.contentSystem = system;
    }

    setOptimizers(overdraw: OverdrawOptimizer, texture: TextureOptimizer) {
        this.overdrawOptimizer = overdraw;
        this.textureOptimizer = texture;
    }

    private initializeMonitoring() {
        setInterval(() => this.monitorPerformance(), 1000);
    }

    async monitorPerformance() {
        if (!this.validateDependencies()) {
            return;
        }

        try {
            const areas = await this.contentSystem!.getActiveAreas();
            
            for (const area of areas) {
                const metrics = await this.collectAreaMetrics(area.id);
                this.areaMetrics.set(area.id, metrics);
                
                this.checkThresholds(metrics);
            }

            await this.generatePerformanceReport();
        } catch (error) {
            this.emitWarning(`Erro no monitoramento de performance: ${error}`);
        }
    }

    private validateDependencies(): boolean {
        if (!this.contentSystem) {
            this.emitWarning('ContentProductionSystem não inicializado');
            return false;
        }
        if (!this.overdrawOptimizer) {
            this.emitWarning('OverdrawOptimizer não inicializado');
            return false;
        }
        if (!this.textureOptimizer) {
            this.emitWarning('TextureOptimizer não inicializado');
            return false;
        }
        return true;
    }

    private async collectAreaMetrics(areaId: string): Promise<AreaPerformanceMetrics> {
        if (!this.validateDependencies()) {
            throw new Error('Dependências não inicializadas');
        }

        const area = await this.contentSystem!.getAreaDetails(areaId);
        if (!area) {
            throw new Error(`Área ${areaId} não encontrada`);
        }
        
        return {
            areaId,
            fps: this.measureFPS(),
            vramUsage: this.getVRAMUsage(),
            drawCalls: this.getDrawCalls(),
            overdraw: await this.overdrawOptimizer!.getOverdrawMetrics(),
            textureMemory: await this.textureOptimizer!.getMemoryUsage(),
            geometryMemory: this.getGeometryMemoryUsage(),
            audioChannels: this.getActiveAudioChannels()
        };
    }

    private checkThresholds(metrics: AreaPerformanceMetrics) {
        if (!this.validateDependencies()) {
            return;
        }

        if (metrics.fps < this.thresholds.targetFPS) {
            this.emitWarning(`FPS abaixo do alvo em ${metrics.areaId}: ${metrics.fps}`);
            this.suggestOptimizations(metrics);
        }

        if (metrics.overdraw > this.thresholds.maxOverdraw) {
            this.emitWarning(`Overdraw excessivo em ${metrics.areaId}: ${metrics.overdraw.toFixed(2)}x`);
            this.suggestOverdrawOptimizations(metrics);
        }

        if (metrics.vramUsage > this.thresholds.maxVRAM) {
            this.emitWarning(`Uso de VRAM acima do limite em ${metrics.areaId}: ${metrics.vramUsage}MB`);
            this.suggestMemoryOptimizations(metrics);
        }

        if (metrics.drawCalls > this.thresholds.maxDrawCalls) {
            this.emitWarning(`Draw calls excessivos em ${metrics.areaId}: ${metrics.drawCalls}`);
            this.suggestDrawCallOptimizations(metrics);
        }
    }

    private suggestOptimizations(metrics: AreaPerformanceMetrics) {
        if (!this.contentSystem) return;

        const suggestions = [];

        if (metrics.overdraw > 1.5) {
            suggestions.push('- Ajustar ordenação de objetos e occlusion culling');
            suggestions.push('- Revisar geometria complexa e sobreposição de objetos');
        }

        if (metrics.textureMemory > this.thresholds.maxTextureMemory * 0.8) {
            suggestions.push('- Comprimir texturas menos visíveis');
            suggestions.push('- Implementar streaming de texturas para a área');
        }

        if (metrics.geometryMemory > this.thresholds.maxGeometryMemory * 0.8) {
            suggestions.push('- Otimizar LODs e decimação de malhas');
            suggestions.push('- Revisar instanciamento de objetos repetidos');
        }

        if (suggestions.length > 0) {
            this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
        }
    }

    private async generatePerformanceReport() {
        if (!this.contentSystem) return;

        const report: PerformanceReport = {
            timestamp: new Date(),
            areas: Array.from(this.areaMetrics.values()),
            globalStats: this.calculateGlobalStats(),
            optimizationSuggestions: this.generateOptimizationSuggestions()
        };

        await this.contentSystem.updatePerformanceReport(report);
    }

    private suggestOverdrawOptimizations(metrics: AreaPerformanceMetrics) {
        if (!this.contentSystem) return;

        const suggestions = [
            'Verificar ordem de renderização de objetos',
            'Ajustar occlusion culling',
            'Otimizar geometria visível',
            'Revisar transparências'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }

    private suggestMemoryOptimizations(metrics: AreaPerformanceMetrics) {
        if (!this.contentSystem) return;

        const suggestions = [
            'Comprimir texturas menos importantes',
            'Implementar streaming de assets',
            'Otimizar LODs',
            'Revisar qualidade de texturas'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }

    private suggestDrawCallOptimizations(metrics: AreaPerformanceMetrics) {
        if (!this.contentSystem) return;

        const suggestions = [
            'Implementar batching de geometria',
            'Usar instancing para objetos repetidos',
            'Otimizar número de materiais',
            'Revisar granularidade de meshes'
        ];
        this.contentSystem.suggestOptimizations(metrics.areaId, suggestions);
    }

    private calculateGlobalStats() {
        let totalFPS = 0;
        let totalVRAM = 0;
        let maxOverdraw = 0;
        let totalAreas = this.areaMetrics.size;

        for (const metrics of this.areaMetrics.values()) {
            totalFPS += metrics.fps;
            totalVRAM += metrics.vramUsage;
            maxOverdraw = Math.max(maxOverdraw, metrics.overdraw);
        }

        return {
            averageFPS: totalFPS / totalAreas,
            totalVRAM,
            maxOverdraw,
            areaCount: totalAreas
        };
    }

    private generateOptimizationSuggestions(): Map<string, string[]> {
        const suggestions = new Map<string, string[]>();
        
        for (const [areaId, metrics] of this.areaMetrics) {
            const areaSuggestions = [];
            
            if (metrics.fps < this.thresholds.targetFPS) {
                areaSuggestions.push('Otimização de Performance Necessária');
            }
            
            if (metrics.overdraw > this.thresholds.maxOverdraw) {
                areaSuggestions.push('Redução de Overdraw Requerida');
            }
            
            if (metrics.vramUsage > this.thresholds.maxVRAM) {
                areaSuggestions.push('Otimização de Memória Necessária');
            }
            
            if (areaSuggestions.length > 0) {
                suggestions.set(areaId, areaSuggestions);
            }
        }
        
        return suggestions;
    }

    private measureFPS(): number {
        return performance.now() / 1000;
    }

    private getVRAMUsage(): number {
        // Implementação real de medição de VRAM
        return 0;
    }

    private getDrawCalls(): number {
        // Implementação real de contagem de draw calls
        return 0;
    }

    private getGeometryMemoryUsage(): number {
        // Implementação real de medição de memória de geometria
        return 0;
    }

    private getActiveAudioChannels(): number {
        // Implementação real de contagem de canais de áudio
        return 0;
    }

    onWarning(callback: (warning: string) => void) {
        this.warningCallbacks.push(callback);
    }

    private emitWarning(warning: string) {
        this.warningCallbacks.forEach(callback => callback(warning));
    }

    getMapProductionStats() {
        // Simula métricas de produção do mapa
        return {
            averageFPS: 850 + Math.random() * 200, // 850-1050 FPS
            vramUsage: 4000 + Math.random() * 2000, // 4-6GB
            drawCalls: 1200 + Math.floor(Math.random() * 600), // 1200-1800
            averageOverdraw: 1.2 + Math.random() * 0.8, // 1.2-2.0
            textureMemory: 2000 + Math.random() * 1000, // 2-3GB
            geometryMemory: 1000 + Math.random() * 500, // 1-1.5GB
            audioChannels: 20 + Math.floor(Math.random() * 20) // 20-40 canais
        };
    }

    getOverdrawMetrics() {
        return {
            averageOverdraw: 1.5 + Math.random() * 0.5,
            maxOverdraw: 2.0 + Math.random() * 1.0,
            pixelsOverdrawn: 1920 * 1080 * (1.5 + Math.random() * 0.5),
            gpuOverdrawTime: 2.5 + Math.random() * 2.0
        };
    }
}