import { Vector3 } from '../../shared/types';
import { GameAudioSystem } from '../../audio/GameAudioSystem';
import { AudioManager } from '../../audio/AudioManager';
import { PerformanceMetrics } from '../../rendering/PerformanceMetrics';

interface AudioZoneSpec {
    id: string;
    type: 'ambient' | 'interactive' | 'dynamic';
    position: Vector3;
    radius: number;
    volume: number;
    priority: number;
    sounds: AudioAssetSpec[];
    reverb: ReverbSettings;
    occlusion: boolean;
}

interface AudioAssetSpec {
    id: string;
    type: 'ambient' | 'sfx' | 'music' | 'voice';
    category: string;
    duration: number;
    loop: boolean;
    volume: number;
    pitch: number;
    spatial: boolean;
    falloffDistance: number;
    compressionFormat: 'ogg' | 'mp3' | 'wav';
    quality: 'low' | 'medium' | 'high';
}

interface ReverbSettings {
    roomSize: number;
    decay: number;
    preDelay: number;
    damping: number;
    earlyReflections: number;
    lateReflections: number;
}

interface SoundscapeProfile {
    name: string;
    ambientLayers: string[];
    interactiveSounds: string[];
    musicTracks: string[];
    reverbProfile: ReverbSettings;
    dynamicRange: { min: number; max: number };
    spatialDensity: number;
}

export class AudioAgent {
    private audioSystem: GameAudioSystem;
    private audioManager: AudioManager;
    private metrics: PerformanceMetrics;
    
    private createdZones: Map<string, AudioZoneSpec> = new Map();
    private audioAssets: Map<string, AudioAssetSpec> = new Map();
    private soundscapeProfiles: Map<string, SoundscapeProfile> = new Map();
    private activeChannels: Set<string> = new Set();

    constructor() {
        this.metrics = PerformanceMetrics.getInstance();
        this.audioManager = new AudioManager();
        this.audioSystem = new GameAudioSystem({
            maxChannels: 64,
            spatialAudio: true,
            reverbEnabled: true,
            compressionEnabled: true
        });
        
        this.initializeSoundscapeProfiles();
    }

    async createPOIAmbientSoundscape(
        poiType: 'urban' | 'military' | 'industrial' | 'forest',
        poiId: string,
        position: Vector3,
        size: { width: number; length: number; height: number }
    ): Promise<{
        ambientZones: AudioZoneSpec[];
        interactiveAudio: AudioAssetSpec[];
        reverbZones: any[];
    }> {
        console.log(`🔊 Audio Agent: Criando soundscape para POI ${poiType} (${poiId})`);

        const soundscapeProfile = this.getSoundscapeProfile(poiType);
        
        // Cria zonas ambientais
        const ambientZones = await this.createAmbientZones(poiType, position, size, soundscapeProfile);
        
        // Cria áudio interativo
        const interactiveAudio = await this.createInteractiveAudio(poiType, soundscapeProfile);
        
        // Configura zonas de reverberação
        const reverbZones = await this.createReverbZones(poiType, position, size);

        // Otimiza áudio para performance
        await this.optimizeAudioPerformance(ambientZones, interactiveAudio);

        console.log(`✅ Soundscape criado: ${ambientZones.length} zonas ambientais, ${interactiveAudio.length} sons interativos`);

        return { ambientZones, interactiveAudio, reverbZones };
    }

    private async createAmbientZones(
        poiType: string,
        position: Vector3,
        size: any,
        profile: SoundscapeProfile
    ): Promise<AudioZoneSpec[]> {
        const zones: AudioZoneSpec[] = [];

        // Zona ambiental principal (cobrindo todo o POI)
        const mainZone = await this.createMainAmbientZone(poiType, position, size, profile);
        zones.push(mainZone);

        // Zonas ambientais específicas baseadas no tipo de POI
        const specificZones = await this.createSpecificAmbientZones(poiType, position, size, profile);
        zones.push(...specificZones);

        // Zonas de transição nas bordas
        const transitionZones = await this.createTransitionZones(poiType, position, size);
        zones.push(...transitionZones);

        return zones;
    }

    private async createMainAmbientZone(
        poiType: string,
        position: Vector3,
        size: any,
        profile: SoundscapeProfile
    ): Promise<AudioZoneSpec> {
        const radius = Math.max(size.width, size.length) * 0.6;
        const ambientSounds = await this.generateAmbientSounds(poiType, profile.ambientLayers);

        const zone: AudioZoneSpec = {
            id: `ambient_main_${poiType}_${Date.now()}`,
            type: 'ambient',
            position,
            radius,
            volume: 0.7,
            priority: 1,
            sounds: ambientSounds,
            reverb: profile.reverbProfile,
            occlusion: false
        };

        this.createdZones.set(zone.id, zone);
        return zone;
    }

    private async createSpecificAmbientZones(
        poiType: string,
        position: Vector3,
        size: any,
        profile: SoundscapeProfile
    ): Promise<AudioZoneSpec[]> {
        const zones: AudioZoneSpec[] = [];

        switch (poiType) {
            case 'urban':
                // Zona de tráfego
                zones.push(await this.createTrafficZone(position, size));
                // Zona de atividade humana
                zones.push(await this.createHumanActivityZone(position, size));
                break;

            case 'military':
                // Zona de equipamentos
                zones.push(await this.createMilitaryEquipmentZone(position, size));
                // Zona de comunicações
                zones.push(await this.createCommunicationZone(position, size));
                break;

            case 'industrial':
                // Zona de maquinário
                zones.push(await this.createMachineryZone(position, size));
                // Zona de vapor/gases
                zones.push(await this.createSteamZone(position, size));
                break;

            case 'forest':
                // Zona de vida selvagem
                zones.push(await this.createWildlifeZone(position, size));
                // Zona de vento nas árvores
                zones.push(await this.createWindZone(position, size));
                break;
        }

        return zones;
    }

    private async generateAmbientSounds(
        poiType: string,
        ambientLayers: string[]
    ): Promise<AudioAssetSpec[]> {
        const sounds: AudioAssetSpec[] = [];

        for (const layer of ambientLayers) {
            const sound = await this.createAmbientSound(layer, poiType);
            sounds.push(sound);
            this.audioAssets.set(sound.id, sound);
        }

        return sounds;
    }

    private async createAmbientSound(
        soundType: string,
        poiType: string
    ): Promise<AudioAssetSpec> {
        const soundSpec: AudioAssetSpec = {
            id: `ambient_${soundType}_${poiType}_${Date.now()}`,
            type: 'ambient',
            category: soundType,
            duration: this.getAmbientSoundDuration(soundType),
            loop: true,
            volume: this.getAmbientSoundVolume(soundType),
            pitch: 1.0,
            spatial: this.isAmbientSoundSpatial(soundType),
            falloffDistance: this.getAmbientFalloffDistance(soundType),
            compressionFormat: 'ogg',
            quality: 'medium'
        };

        return soundSpec;
    }

    private async createInteractiveAudio(
        poiType: string,
        profile: SoundscapeProfile
    ): Promise<AudioAssetSpec[]> {
        const interactiveAudio: AudioAssetSpec[] = [];

        for (const soundType of profile.interactiveSounds) {
            const sound = await this.createInteractiveSound(soundType, poiType);
            interactiveAudio.push(sound);
            this.audioAssets.set(sound.id, sound);
        }

        return interactiveAudio;
    }

    private async createInteractiveSound(
        soundType: string,
        poiType: string
    ): Promise<AudioAssetSpec> {
        const soundSpec: AudioAssetSpec = {
            id: `interactive_${soundType}_${poiType}_${Date.now()}`,
            type: 'sfx',
            category: soundType,
            duration: this.getInteractiveSoundDuration(soundType),
            loop: false,
            volume: this.getInteractiveSoundVolume(soundType),
            pitch: 1.0 + (Math.random() - 0.5) * 0.2, // Variação de pitch
            spatial: true,
            falloffDistance: this.getInteractiveFalloffDistance(soundType),
            compressionFormat: 'ogg',
            quality: 'high'
        };

        return soundSpec;
    }

    private async createReverbZones(
        poiType: string,
        position: Vector3,
        size: any
    ): Promise<any[]> {
        const reverbZones = [];
        const reverbSettings = this.getReverbSettingsForPOI(poiType);

        // Zona de reverberação principal
        reverbZones.push({
            id: `reverb_main_${poiType}`,
            position,
            size: { width: size.width * 1.2, length: size.length * 1.2, height: size.height },
            settings: reverbSettings.outdoor
        });

        // Zonas de reverberação para interiores (se aplicável)
        if (poiType === 'urban' || poiType === 'military' || poiType === 'industrial') {
            const interiorZones = this.createInteriorReverbZones(poiType, position, size);
            reverbZones.push(...interiorZones);
        }

        return reverbZones;
    }

    private createInteriorReverbZones(poiType: string, position: Vector3, size: any): any[] {
        const zones = [];
        const reverbSettings = this.getReverbSettingsForPOI(poiType);

        // Simula 2-4 espaços interiores
        const interiorCount = 2 + Math.floor(Math.random() * 3);
        
        for (let i = 0; i < interiorCount; i++) {
            const interiorPosition = {
                x: position.x + (Math.random() - 0.5) * size.width * 0.8,
                y: position.y,
                z: position.z + (Math.random() - 0.5) * size.length * 0.8
            };

            zones.push({
                id: `reverb_interior_${poiType}_${i}`,
                position: interiorPosition,
                size: { width: 10, length: 10, height: 3 },
                settings: reverbSettings.interior
            });
        }

        return zones;
    }

    private initializeSoundscapeProfiles() {
        this.soundscapeProfiles.set('urban', {
            name: 'Urban Environment',
            ambientLayers: ['city_traffic', 'distant_voices', 'urban_wind', 'electrical_hum'],
            interactiveSounds: ['footsteps_concrete', 'door_slam', 'car_horn', 'glass_break'],
            musicTracks: ['urban_tension', 'city_exploration'],
            reverbProfile: {
                roomSize: 0.8,
                decay: 1.2,
                preDelay: 0.02,
                damping: 0.6,
                earlyReflections: 0.7,
                lateReflections: 0.5
            },
            dynamicRange: { min: 0.3, max: 0.9 },
            spatialDensity: 0.7
        });

        this.soundscapeProfiles.set('military', {
            name: 'Military Base',
            ambientLayers: ['radio_chatter', 'generator_hum', 'metal_creaking', 'distant_engines'],
            interactiveSounds: ['metal_footsteps', 'weapon_handling', 'radio_static', 'equipment_rattle'],
            musicTracks: ['military_tension', 'tactical_ambient'],
            reverbProfile: {
                roomSize: 0.6,
                decay: 0.8,
                preDelay: 0.01,
                damping: 0.8,
                earlyReflections: 0.6,
                lateReflections: 0.4
            },
            dynamicRange: { min: 0.2, max: 0.8 },
            spatialDensity: 0.5
        });

        this.soundscapeProfiles.set('industrial', {
            name: 'Industrial Zone',
            ambientLayers: ['machinery_hum', 'steam_vents', 'metal_clanking', 'electrical_buzz'],
            interactiveSounds: ['metal_impact', 'steam_release', 'machinery_start', 'warning_beep'],
            musicTracks: ['industrial_ambient', 'mechanical_tension'],
            reverbProfile: {
                roomSize: 0.9,
                decay: 1.5,
                preDelay: 0.03,
                damping: 0.4,
                earlyReflections: 0.8,
                lateReflections: 0.7
            },
            dynamicRange: { min: 0.4, max: 1.0 },
            spatialDensity: 0.8
        });

        this.soundscapeProfiles.set('forest', {
            name: 'Forest Environment',
            ambientLayers: ['wind_through_trees', 'bird_songs', 'rustling_leaves', 'distant_water'],
            interactiveSounds: ['branch_snap', 'leaves_rustle', 'animal_movement', 'water_splash'],
            musicTracks: ['forest_calm', 'nature_exploration'],
            reverbProfile: {
                roomSize: 1.0,
                decay: 2.0,
                preDelay: 0.05,
                damping: 0.3,
                earlyReflections: 0.4,
                lateReflections: 0.8
            },
            dynamicRange: { min: 0.1, max: 0.7 },
            spatialDensity: 0.6
        });
    }

    private getSoundscapeProfile(poiType: string): SoundscapeProfile {
        return this.soundscapeProfiles.get(poiType) || this.soundscapeProfiles.get('urban')!;
    }

    private async createTrafficZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `traffic_zone_${Date.now()}`,
            type: 'ambient',
            position: { x: position.x, y: position.y, z: position.z - size.length * 0.4 },
            radius: 30,
            volume: 0.5,
            priority: 2,
            sounds: [await this.createAmbientSound('traffic', 'urban')],
            reverb: this.getDefaultReverb(),
            occlusion: true
        };
    }

    private async createHumanActivityZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `human_activity_zone_${Date.now()}`,
            type: 'dynamic',
            position: { x: position.x + size.width * 0.2, y: position.y, z: position.z },
            radius: 25,
            volume: 0.4,
            priority: 3,
            sounds: [
                await this.createAmbientSound('distant_voices', 'urban'),
                await this.createAmbientSound('footsteps', 'urban')
            ],
            reverb: this.getDefaultReverb(),
            occlusion: true
        };
    }

    private async createMilitaryEquipmentZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `military_equipment_zone_${Date.now()}`,
            type: 'ambient',
            position,
            radius: 35,
            volume: 0.6,
            priority: 2,
            sounds: [
                await this.createAmbientSound('generator_hum', 'military'),
                await this.createAmbientSound('equipment_rattle', 'military')
            ],
            reverb: this.getMilitaryReverb(),
            occlusion: false
        };
    }

    private async createCommunicationZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `communication_zone_${Date.now()}`,
            type: 'interactive',
            position: { x: position.x, y: position.y + 5, z: position.z },
            radius: 20,
            volume: 0.3,
            priority: 4,
            sounds: [await this.createAmbientSound('radio_chatter', 'military')],
            reverb: this.getMilitaryReverb(),
            occlusion: true
        };
    }

    private async createMachineryZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `machinery_zone_${Date.now()}`,
            type: 'ambient',
            position,
            radius: 40,
            volume: 0.8,
            priority: 1,
            sounds: [
                await this.createAmbientSound('machinery_hum', 'industrial'),
                await this.createAmbientSound('metal_clanking', 'industrial')
            ],
            reverb: this.getIndustrialReverb(),
            occlusion: false
        };
    }

    private async createSteamZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `steam_zone_${Date.now()}`,
            type: 'dynamic',
            position: { x: position.x - size.width * 0.3, y: position.y + 3, z: position.z },
            radius: 15,
            volume: 0.7,
            priority: 3,
            sounds: [await this.createAmbientSound('steam_vents', 'industrial')],
            reverb: this.getIndustrialReverb(),
            occlusion: true
        };
    }

    private async createWildlifeZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `wildlife_zone_${Date.now()}`,
            type: 'dynamic',
            position,
            radius: 50,
            volume: 0.5,
            priority: 2,
            sounds: [
                await this.createAmbientSound('bird_songs', 'forest'),
                await this.createAmbientSound('animal_movement', 'forest')
            ],
            reverb: this.getForestReverb(),
            occlusion: false
        };
    }

    private async createWindZone(position: Vector3, size: any): Promise<AudioZoneSpec> {
        return {
            id: `wind_zone_${Date.now()}`,
            type: 'ambient',
            position: { x: position.x, y: position.y + size.height * 0.7, z: position.z },
            radius: 60,
            volume: 0.4,
            priority: 1,
            sounds: [await this.createAmbientSound('wind_through_trees', 'forest')],
            reverb: this.getForestReverb(),
            occlusion: false
        };
    }

    private async createTransitionZones(
        poiType: string,
        position: Vector3,
        size: any
    ): Promise<AudioZoneSpec[]> {
        const zones: AudioZoneSpec[] = [];
        const transitionRadius = 20;

        // Cria zonas de transição nas 4 direções
        const directions = [
            { x: size.width * 0.5, z: 0 },    // Leste
            { x: -size.width * 0.5, z: 0 },   // Oeste
            { x: 0, z: size.length * 0.5 },   // Norte
            { x: 0, z: -size.length * 0.5 }   // Sul
        ];

        for (let i = 0; i < directions.length; i++) {
            const dir = directions[i];
            const transitionPosition = {
                x: position.x + dir.x,
                y: position.y,
                z: position.z + dir.z
            };

            const zone: AudioZoneSpec = {
                id: `transition_${poiType}_${i}_${Date.now()}`,
                type: 'ambient',
                position: transitionPosition,
                radius: transitionRadius,
                volume: 0.3,
                priority: 5,
                sounds: [await this.createAmbientSound('ambient_transition', poiType)],
                reverb: this.getTransitionReverb(),
                occlusion: false
            };

            zones.push(zone);
            this.createdZones.set(zone.id, zone);
        }

        return zones;
    }

    private getAmbientSoundDuration(soundType: string): number {
        const durations = {
            city_traffic: 30,
            distant_voices: 25,
            urban_wind: 20,
            electrical_hum: 15,
            radio_chatter: 10,
            generator_hum: 45,
            metal_creaking: 8,
            machinery_hum: 60,
            steam_vents: 12,
            wind_through_trees: 35,
            bird_songs: 20,
            rustling_leaves: 15
        };
        return durations[soundType] || 20;
    }

    private getAmbientSoundVolume(soundType: string): number {
        const volumes = {
            city_traffic: 0.6,
            distant_voices: 0.3,
            urban_wind: 0.4,
            electrical_hum: 0.2,
            radio_chatter: 0.4,
            generator_hum: 0.7,
            machinery_hum: 0.8,
            steam_vents: 0.5,
            wind_through_trees: 0.5,
            bird_songs: 0.4
        };
        return volumes[soundType] || 0.5;
    }

    private isAmbientSoundSpatial(soundType: string): boolean {
        const spatialSounds = [
            'radio_chatter', 'steam_vents', 'bird_songs', 'animal_movement',
            'metal_creaking', 'equipment_rattle'
        ];
        return spatialSounds.includes(soundType);
    }

    private getAmbientFalloffDistance(soundType: string): number {
        const distances = {
            city_traffic: 100,
            machinery_hum: 80,
            generator_hum: 60,
            wind_through_trees: 120,
            radio_chatter: 30,
            steam_vents: 25,
            bird_songs: 70
        };
        return distances[soundType] || 50;
    }

    private getInteractiveSoundDuration(soundType: string): number {
        const durations = {
            footsteps_concrete: 0.5,
            door_slam: 1.2,
            car_horn: 2.0,
            glass_break: 0.8,
            metal_footsteps: 0.4,
            weapon_handling: 1.5,
            radio_static: 0.3,
            metal_impact: 0.6,
            steam_release: 2.5,
            branch_snap: 0.4,
            water_splash: 1.0
        };
        return durations[soundType] || 1.0;
    }

    private getInteractiveSoundVolume(soundType: string): number {
        const volumes = {
            footsteps_concrete: 0.4,
            door_slam: 0.8,
            car_horn: 0.9,
            glass_break: 0.7,
            weapon_handling: 0.6,
            metal_impact: 0.8,
            steam_release: 0.7,
            branch_snap: 0.5,
            water_splash: 0.6
        };
        return volumes[soundType] || 0.6;
    }

    private getInteractiveFalloffDistance(soundType: string): number {
        const distances = {
            footsteps_concrete: 15,
            door_slam: 40,
            car_horn: 150,
            glass_break: 50,
            weapon_handling: 25,
            metal_impact: 60,
            steam_release: 35,
            branch_snap: 20,
            water_splash: 30
        };
        return distances[soundType] || 30;
    }

    private getDefaultReverb(): ReverbSettings {
        return {
            roomSize: 0.5,
            decay: 1.0,
            preDelay: 0.02,
            damping: 0.5,
            earlyReflections: 0.5,
            lateReflections: 0.5
        };
    }

    private getMilitaryReverb(): ReverbSettings {
        return {
            roomSize: 0.6,
            decay: 0.8,
            preDelay: 0.01,
            damping: 0.8,
            earlyReflections: 0.6,
            lateReflections: 0.4
        };
    }

    private getIndustrialReverb(): ReverbSettings {
        return {
            roomSize: 0.9,
            decay: 1.5,
            preDelay: 0.03,
            damping: 0.4,
            earlyReflections: 0.8,
            lateReflections: 0.7
        };
    }

    private getForestReverb(): ReverbSettings {
        return {
            roomSize: 1.0,
            decay: 2.0,
            preDelay: 0.05,
            damping: 0.3,
            earlyReflections: 0.4,
            lateReflections: 0.8
        };
    }

    private getTransitionReverb(): ReverbSettings {
        return {
            roomSize: 0.7,
            decay: 1.2,
            preDelay: 0.025,
            damping: 0.6,
            earlyReflections: 0.5,
            lateReflections: 0.6
        };
    }

    private getReverbSettingsForPOI(poiType: string): { outdoor: ReverbSettings; interior: ReverbSettings } {
        const settings = {
            urban: {
                outdoor: this.getDefaultReverb(),
                interior: {
                    roomSize: 0.3,
                    decay: 0.6,
                    preDelay: 0.01,
                    damping: 0.7,
                    earlyReflections: 0.8,
                    lateReflections: 0.3
                }
            },
            military: {
                outdoor: this.getMilitaryReverb(),
                interior: {
                    roomSize: 0.4,
                    decay: 0.5,
                    preDelay: 0.005,
                    damping: 0.9,
                    earlyReflections: 0.9,
                    lateReflections: 0.2
                }
            },
            industrial: {
                outdoor: this.getIndustrialReverb(),
                interior: {
                    roomSize: 0.8,
                    decay: 1.2,
                    preDelay: 0.02,
                    damping: 0.3,
                    earlyReflections: 0.9,
                lateReflections: 0.6
                }
            },
            forest: {
                outdoor: this.getForestReverb(),
                interior: {
                    roomSize: 0.2,
                    decay: 0.4,
                    preDelay: 0.01,
                    damping: 0.8,
                    earlyReflections: 0.6,
                    lateReflections: 0.2
                }
            }
        };

        return settings[poiType] || settings.urban;
    }

    private async optimizeAudioPerformance(
        ambientZones: AudioZoneSpec[],
        interactiveAudio: AudioAssetSpec[]
    ): Promise<void> {
        console.log('🔧 Otimizando performance de áudio...');

        // Monitora canais ativos
        const activeChannels = this.metrics.getActiveAudioChannels();
        const maxChannels = 64;

        if (activeChannels > maxChannels * 0.8) {
            // Reduz qualidade de áudio se muitos canais estão ativos
            for (const zone of ambientZones) {
                if (zone.priority > 3) {
                    zone.volume *= 0.8;
                }
            }

            for (const audio of interactiveAudio) {
                if (audio.quality === 'high') {
                    audio.quality = 'medium';
                }
            }
        }

        // Otimiza compressão baseado na performance
        const currentFPS = this.metrics.measureFPS();
        if (currentFPS < 500) {
            for (const audio of interactiveAudio) {
                if (audio.compressionFormat === 'wav') {
                    audio.compressionFormat = 'ogg';
                }
            }
        }

        console.log(`✅ Áudio otimizado: ${ambientZones.length} zonas, ${interactiveAudio.length} sons interativos`);
    }

    async getCreatedZones(): Promise<AudioZoneSpec[]> {
        return Array.from(this.createdZones.values());
    }

    async getAudioAssets(): Promise<AudioAssetSpec[]> {
        return Array.from(this.audioAssets.values());
    }

    async getSoundscapeProfiles(): Promise<Map<string, SoundscapeProfile>> {
        return this.soundscapeProfiles;
    }

    async getActiveChannels(): Promise<Set<string>> {
        return this.activeChannels;
    }
}
