import { ContentOrchestrator } from '../content/agents/ContentOrchestrator';
import { PerformanceMetrics } from '../rendering/PerformanceMetrics';

interface ProductionTestConfig {
    enableDetailedLogging: boolean;
    performanceMonitoringInterval: number;
    targetFPS: number;
    maxExecutionTime: number;
    generateAssets: boolean;
}

class MassProductionTest {
    private orchestrator: ContentOrchestrator;
    private metrics: PerformanceMetrics;
    private config: ProductionTestConfig;
    private startTime: number = 0;
    private monitoringInterval?: NodeJS.Timeout;

    constructor(config: Partial<ProductionTestConfig> = {}) {
        this.config = {
            enableDetailedLogging: true,
            performanceMonitoringInterval: 5000, // 5 segundos
            targetFPS: 999,
            maxExecutionTime: 1800000, // 30 minutos
            generateAssets: true,
            ...config
        };

        this.orchestrator = new ContentOrchestrator();
        this.metrics = PerformanceMetrics.getInstance();
    }

    async runFullProductionTest(): Promise<void> {
        console.log('🚀 INICIANDO TESTE DE PRODUÇÃO EM MASSA DO TACTICAL NEXUS');
        console.log('=========================================================');
        console.log(`Configuração do teste:`);
        console.log(`- Meta de FPS: ${this.config.targetFPS}`);
        console.log(`- Tempo máximo: ${this.config.maxExecutionTime / 60000} minutos`);
        console.log(`- Geração de assets: ${this.config.generateAssets ? 'Ativada' : 'Desativada'}`);
        console.log(`- Logging detalhado: ${this.config.enableDetailedLogging ? 'Ativado' : 'Desativado'}`);
        console.log('');

        this.startTime = Date.now();

        try {
            // Inicia monitoramento de performance
            this.startPerformanceMonitoring();

            // Executa produção em massa
            await this.orchestrator.startMassProduction();

            // Para monitoramento
            this.stopPerformanceMonitoring();

            // Gera relatório final
            await this.generateTestReport();

            console.log('\n✅ TESTE DE PRODUÇÃO EM MASSA CONCLUÍDO COM SUCESSO!');
        } catch (error) {
            this.stopPerformanceMonitoring();
            console.error('\n❌ TESTE DE PRODUÇÃO EM MASSA FALHOU:', error);
            await this.generateErrorReport(error);
            throw error;
        }
    }

    private startPerformanceMonitoring(): void {
        if (!this.config.enableDetailedLogging) return;

        console.log('📊 Iniciando monitoramento de performance...\n');

        this.monitoringInterval = setInterval(async () => {
            const status = await this.orchestrator.getProductionStatus();
            const elapsedTime = (Date.now() - this.startTime) / 1000;

            console.log(`⏱️  Tempo decorrido: ${elapsedTime.toFixed(1)}s`);
            console.log(`📈 Progresso geral: ${status.overallProgress.toFixed(1)}%`);
            console.log(`🎯 FPS atual: ${status.currentMetrics.averageFPS.toFixed(2)}`);
            console.log(`💾 VRAM: ${status.currentMetrics.vramUsage.toFixed(0)} MB`);
            console.log(`🔄 Draw calls: ${status.currentMetrics.drawCalls}`);
            
            // Mostra tarefas em andamento
            const activeTasks = status.tasks.filter(task => task.status === 'in_progress');
            if (activeTasks.length > 0) {
                console.log(`🔧 Tarefas ativas: ${activeTasks.map(t => `${t.id} (${t.progress}%)`).join(', ')}`);
            }

            console.log('─'.repeat(60));
        }, this.config.performanceMonitoringInterval);
    }

    private stopPerformanceMonitoring(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
    }

    private async generateTestReport(): Promise<void> {
        const totalTime = (Date.now() - this.startTime) / 1000;
        const status = await this.orchestrator.getProductionStatus();
        const reports = await this.orchestrator.getProductionReports();
        const poiSpecs = await this.orchestrator.getPOISpecs();

        console.log('\n📋 RELATÓRIO DETALHADO DO TESTE');
        console.log('================================');
        
        // Informações gerais
        console.log(`⏱️  Tempo total de execução: ${totalTime.toFixed(2)}s (${(totalTime/60).toFixed(1)} min)`);
        console.log(`📊 Progresso final: ${status.overallProgress.toFixed(1)}%`);
        console.log(`✅ Tarefas concluídas: ${status.tasks.filter(t => t.status === 'completed').length}/${status.tasks.length}`);
        console.log(`❌ Tarefas falhadas: ${status.tasks.filter(t => t.status === 'failed').length}`);

        // Métricas de performance
        console.log('\n🎯 MÉTRICAS DE PERFORMANCE FINAL');
        console.log('─'.repeat(40));
        console.log(`FPS médio: ${status.currentMetrics.averageFPS.toFixed(2)} (meta: ${this.config.targetFPS})`);
        console.log(`VRAM utilizada: ${status.currentMetrics.vramUsage.toFixed(0)} MB`);
        console.log(`Draw calls: ${status.currentMetrics.drawCalls}`);
        console.log(`Overdraw médio: ${status.currentMetrics.averageOverdraw?.toFixed(2) || 'N/A'}`);

        // Análise de POIs
        console.log('\n🏗️  ANÁLISE DOS POIs PRODUZIDOS');
        console.log('─'.repeat(40));
        const poiByType = this.groupPOIsByType(poiSpecs);
        
        for (const [type, pois] of Object.entries(poiByType)) {
            console.log(`${type.toUpperCase()}: ${pois.length} POIs`);
            pois.forEach(poi => {
                const hasDesign = poi.designSpec ? '✅' : '❌';
                const hasArt = poi.artAssets ? '✅' : '❌';
                const hasAudio = poi.audioSetup ? '✅' : '❌';
                console.log(`  - ${poi.id}: Design ${hasDesign} | Arte ${hasArt} | Áudio ${hasAudio}`);
            });
        }

        // Análise de tempo por tarefa
        console.log('\n⏱️  ANÁLISE DE TEMPO POR TAREFA');
        console.log('─'.repeat(40));
        const completedTasks = status.tasks.filter(t => t.status === 'completed' && t.actualTime);
        completedTasks.forEach(task => {
            const efficiency = task.actualTime! <= task.estimatedTime ? '✅' : '⚠️';
            const timeRatio = (task.actualTime! / task.estimatedTime * 100).toFixed(1);
            console.log(`${efficiency} ${task.id}: ${(task.actualTime! / 1000).toFixed(1)}s (${timeRatio}% do estimado)`);
        });

        // Verificação de metas
        console.log('\n🎯 VERIFICAÇÃO DE METAS');
        console.log('─'.repeat(40));
        const fpsTarget = status.currentMetrics.averageFPS >= this.config.targetFPS * 0.95;
        const vramTarget = status.currentMetrics.vramUsage <= 8192; // 8GB
        const drawCallTarget = status.currentMetrics.drawCalls <= 2000;
        const completionTarget = status.overallProgress >= 95;

        console.log(`${fpsTarget ? '✅' : '❌'} FPS Target (${this.config.targetFPS}): ${status.currentMetrics.averageFPS.toFixed(2)}`);
        console.log(`${vramTarget ? '✅' : '❌'} VRAM Target (8GB): ${status.currentMetrics.vramUsage.toFixed(0)} MB`);
        console.log(`${drawCallTarget ? '✅' : '❌'} Draw Calls Target (2000): ${status.currentMetrics.drawCalls}`);
        console.log(`${completionTarget ? '✅' : '❌'} Completion Target (95%): ${status.overallProgress.toFixed(1)}%`);

        const allTargetsMet = fpsTarget && vramTarget && drawCallTarget && completionTarget;
        console.log(`\n🏆 RESULTADO GERAL: ${allTargetsMet ? 'SUCESSO TOTAL' : 'NECESSITA AJUSTES'}`);

        // Recomendações
        if (!allTargetsMet) {
            console.log('\n💡 RECOMENDAÇÕES PARA MELHORIA');
            console.log('─'.repeat(40));
            
            if (!fpsTarget) {
                console.log('- Reduzir densidade de polígonos em assets menos importantes');
                console.log('- Implementar LODs mais agressivos');
                console.log('- Otimizar shaders e materiais');
            }
            
            if (!vramTarget) {
                console.log('- Comprimir texturas adicionalmente');
                console.log('- Usar atlas de texturas para reduzir fragmentação');
                console.log('- Implementar streaming de texturas');
            }
            
            if (!drawCallTarget) {
                console.log('- Aumentar instanciamento de objetos similares');
                console.log('- Combinar meshes estáticas');
                console.log('- Usar técnicas de batching');
            }
        }
    }

    private async generateErrorReport(error: any): Promise<void> {
        const totalTime = (Date.now() - this.startTime) / 1000;
        const status = await this.orchestrator.getProductionStatus();

        console.log('\n💥 RELATÓRIO DE ERRO');
        console.log('====================');
        console.log(`⏱️  Tempo até falha: ${totalTime.toFixed(2)}s`);
        console.log(`📊 Progresso no momento da falha: ${status.overallProgress.toFixed(1)}%`);
        console.log(`❌ Erro: ${error.message || error}`);

        // Mostra estado das tarefas no momento da falha
        console.log('\n📋 Estado das tarefas:');
        status.tasks.forEach(task => {
            const statusIcon = {
                'completed': '✅',
                'in_progress': '🔄',
                'pending': '⏳',
                'failed': '❌'
            }[task.status] || '❓';
            
            console.log(`${statusIcon} ${task.id}: ${task.status} (${task.progress}%)`);
        });

        // Métricas no momento da falha
        if (status.currentMetrics) {
            console.log('\n📊 Métricas no momento da falha:');
            console.log(`FPS: ${status.currentMetrics.averageFPS?.toFixed(2) || 'N/A'}`);
            console.log(`VRAM: ${status.currentMetrics.vramUsage?.toFixed(0) || 'N/A'} MB`);
            console.log(`Draw calls: ${status.currentMetrics.drawCalls || 'N/A'}`);
        }
    }

    private groupPOIsByType(poiSpecs: any[]): Record<string, any[]> {
        return poiSpecs.reduce((groups, poi) => {
            const type = poi.type;
            if (!groups[type]) {
                groups[type] = [];
            }
            groups[type].push(poi);
            return groups;
        }, {});
    }

    async runQuickTest(): Promise<void> {
        console.log('⚡ Executando teste rápido (apenas validação de sistemas)...');
        
        // Testa apenas a inicialização dos agentes
        try {
            const status = await this.orchestrator.getProductionStatus();
            console.log(`✅ ContentOrchestrator inicializado: ${status.tasks.length} tarefas planejadas`);
            
            const poiSpecs = await this.orchestrator.getPOISpecs();
            console.log(`✅ POI Specs carregadas: ${poiSpecs.length} POIs definidos`);
            
            console.log('✅ Teste rápido concluído - sistemas funcionais');
        } catch (error) {
            console.error('❌ Teste rápido falhou:', error);
            throw error;
        }
    }
}

// Execução do teste
async function main() {
    const args = process.argv.slice(2);
    const isQuickTest = args.includes('--quick');
    const enableLogging = !args.includes('--no-logging');
    const targetFPS = parseInt(args.find(arg => arg.startsWith('--fps='))?.split('=')[1] || '999');

    const test = new MassProductionTest({
        enableDetailedLogging: enableLogging,
        targetFPS: targetFPS,
        generateAssets: !isQuickTest
    });

    try {
        if (isQuickTest) {
            await test.runQuickTest();
        } else {
            await test.runFullProductionTest();
        }
        
        process.exit(0);
    } catch (error) {
        console.error('\n💥 Teste falhou:', error);
        process.exit(1);
    }
}

// Executa apenas se chamado diretamente
if (require.main === module) {
    main().catch(console.error);
}

export { MassProductionTest };
